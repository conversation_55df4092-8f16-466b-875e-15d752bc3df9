using UnityEngine;
using Fusion;

namespace SimpleFPS {
    /// <summary>
    /// Handles network events for spawning essential objects.
    /// This script should be attached to a GameObject and connected to NetworkEvents.
    /// </summary>
    public class NetworkEventsHandler : MonoBehaviour {
        [Header("Essential Object Prefabs")]
        [Tooltip("GameManager prefab to spawn on server")]
        public GameObject gameManagerPrefab;
        [<PERSON><PERSON><PERSON>("GameUI prefab to create for clients")]
        public GameObject gameUIPrefab;
        [<PERSON>lt<PERSON>("PlayerCamera prefab to create for clients")]
        public GameObject playerCameraPrefab;

        private NetworkRunner runner;
        private bool gameManagerSpawned = false;

        private void Start() {
            runner = GetComponent<NetworkRunner>();
            if (runner == null) {
                Debug.LogError("[NetworkEventsHandler] No NetworkRunner found on this GameObject!");
            }
        }

        /// <summary>
        /// Called when a player joins the game.
        /// This method should be connected to NetworkEvents.PlayerJoined event.
        /// </summary>
        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player) {
            // Server: Spawn GameManager on first player join
            if (runner.IsServer && !gameManagerSpawned && GameManager.Instance == null) {
                SpawnGameManager();
            }

            // All clients need essential objects
            // Delay to ensure GameManager is received from server
            Invoke(nameof(EnsureClientObjects), 0.5f);
        }

        /// <summary>
        /// Called when a player leaves the game.
        /// This method should be connected to NetworkEvents.PlayerLeft event.
        /// </summary>
        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player) {
            // Handle player leaving if needed
        }

        private void SpawnGameManager() {
            if (gameManagerPrefab == null) {
                Debug.LogError("[NetworkEventsHandler] GameManager prefab not assigned!");
                return;
            }

            var gameManagerObj = runner.Spawn(gameManagerPrefab, Vector3.zero, Quaternion.identity);

            if (gameManagerObj != null) {
                runner.MakeDontDestroyOnLoad(gameManagerObj.gameObject);
                gameManagerSpawned = true;
            }
            else {
                Debug.LogError("[NetworkEventsHandler] Failed to spawn GameManager!");
            }
        }

        private void EnsureClientObjects() {
            // Check if essential objects already exist
            bool hasGameUI = UIGame.Instance != null;
            bool hasCamera = Camera.main != null;

            // Create GameUI if missing
            if (!hasGameUI && gameUIPrefab != null) {
                var gameUIObj = Instantiate(gameUIPrefab);
                if (gameUIObj != null) {
                    // Set up references
                    var uiGame = gameUIObj.GetComponent<UIGame>();
                    if (uiGame != null && GameManager.Instance != null) {
                        uiGame.GameManager = GameManager.Instance;
                        uiGame.Runner = runner;

                        // Also set GameManager's reference
                        if (GameManager.Instance.gameUI == null) {
                            GameManager.Instance.gameUI = uiGame;
                        }
                    }
                }
                else {
                    Debug.LogError("[NetworkEventsHandler] Failed to create GameUI!");
                }
            }

            // Create PlayerCamera if missing
            if (!hasCamera && playerCameraPrefab != null) {
                var cameraObj = Instantiate(playerCameraPrefab);
                if (cameraObj == null) {
                    Debug.LogError("[NetworkEventsHandler] Failed to create PlayerCamera!");
                }
            }

            // If GameManager doesn't exist yet, retry later
            if (GameManager.Instance == null) {
                Invoke(nameof(EnsureClientObjects), 1f);
            }
        }
    }
}
