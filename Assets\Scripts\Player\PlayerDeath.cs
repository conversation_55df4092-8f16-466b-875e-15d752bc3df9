// PlayerDeath.cs
using UnityEngine;
using Fusion;
using System.Collections;
using SimpleFPS;

public class PlayerDeath : NetworkBehaviour {
    [Header("Visual Effects")]
    public GameObject playerChunkFx;
    public GameObject bloodWallFx;
    public GameObject chunkFx;
    public GameObject bloodSplashFx;

    [Header("Death Audio")]
    public AudioClip deathSound;
    public AudioSource deathAudioSource;

    public void DieFromGrinder(PlayerRef killer) {
        if (!Object.HasStateAuthority) {
            return;
        }

        int seed = Random.Range(int.MinValue, int.MaxValue);
        RPC_Grinded(transform.position, seed);

        // Play death sound for all clients
        RPC_PlayDeathSound();

        // Immediately disable third person model and movement on all clients
        RPC_DisableThirdPersonModel();
        
        RPC_DisablePlayerMovement();

        StartCoroutine(DespawnNextTick());
    }
    public void DieFromExplosion(PlayerRef killer) {
        if (!Object.HasStateAuthority) {
            return;
        }

        int seed = Random.Range(int.MinValue, int.MaxValue);
        RPC_Explode(transform.position, seed);

        // Play death sound for all clients
        RPC_PlayDeathSound();

        // Immediately disable third person model and movement on all clients
        RPC_DisableThirdPersonModel();
        RPC_DisablePlayerMovement();

        StartCoroutine(DespawnNextTick());
    }

    private IEnumerator DespawnNextTick() {
        // Wait 1 second to ensure death sound has time to play
        yield return new WaitForSeconds(1.0f);
        Runner.Despawn(Object);
    }


    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    void RPC_Explode(Vector3 pos, int seed) {
        if (bloodSplashFx) {
            bloodSplashFx.transform.position = pos;
            //bloodSplashFx.SetActive(false); // Reset the effect
            bloodSplashFx.SetActive(true);  // Activate to play the effect
        }
        if (playerChunkFx) {
            playerChunkFx.transform.position = pos;
            //playerChunkFx.SetActive(false); // Reset the effect
            playerChunkFx.SetActive(true);  // Activate to play the effect
        }
        if (chunkFx) {
            chunkFx.transform.position = pos;
            //chunkFx.SetActive(false); // Reset the effect
            chunkFx.SetActive(true);  // Activate to play the effect
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_Grinded(Vector3 pos, int seed) {
        if (playerChunkFx) {
            playerChunkFx.transform.position = pos;
            //playerChunkFx.SetActive(false); // Reset the effect
            playerChunkFx.SetActive(true);  // Activate to play the effect
        }
        if (bloodWallFx) {
            bloodWallFx.transform.position = pos;
            //bloodWallFx.SetActive(false); // Reset the effect
            bloodWallFx.SetActive(true);  // Activate to play the effect
        }
        if (chunkFx) {
            chunkFx.transform.position = pos;
            //chunkFx.SetActive(false); // Reset the effect
            chunkFx.SetActive(true);  // Activate to play the effect
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_PlayDeathSound() {
        if (deathAudioSource != null && deathSound != null) {
            deathAudioSource.PlayOneShot(deathSound);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_DisableThirdPersonModel() {
        // Get the PlayerController component to access thirdPersonRoot
        PlayerController playerController = GetComponent<PlayerController>();
        if (playerController != null && playerController.thirdPersonRoot != null) {
            // Disable the third person model immediately
            playerController.thirdPersonRoot.SetActive(false);
        }
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    private void RPC_DisablePlayerMovement() {
        // Get the PlayerHealth component to set CurrentHealth to 0
        PlayerHealth playerHealth = GetComponent<PlayerHealth>();
        if (playerHealth != null && Object.HasStateAuthority) {
            // Apply enough damage to kill the player (set CurrentHealth to 0)
            // This makes IsAlive return false and prevents movement in PlayerController
            playerHealth.ApplyDamage(EDamageSource.Environment, playerHealth.CurrentHealth, transform.position, Vector3.up);
        }
    }
}
