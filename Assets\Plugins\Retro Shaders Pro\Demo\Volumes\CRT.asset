%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: CRT
  m_EditorClassIdentifier: 
  components:
  - {fileID: 576815941172598958}
  - {fileID: 2182205748910702091}
  - {fileID: 5132782315067736101}
  - {fileID: 8274834959362863516}
--- !u!114 &576815941172598958
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 25792a1c800b6f5498b76a67b3c5b765, type: 3}
  m_Name: CRTSettings
  m_EditorClassIdentifier: 
  active: 1
  showInSceneView:
    m_OverrideState: 1
    m_Value: 0
  enabled:
    m_OverrideState: 1
    m_Value: 1
  renderPassEvent:
    m_OverrideState: 1
    m_Value: 1
  distortionStrength:
    m_OverrideState: 1
    m_Value: 0.15
  distortionSmoothing:
    m_OverrideState: 1
    m_Value: 0.015
  backgroundColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  scaleParameters:
    m_OverrideState: 1
    m_Value: 1
  verticalReferenceResolution:
    m_OverrideState: 1
    m_Value: 1080
  forcePointFiltering:
    m_OverrideState: 1
    m_Value: 0
  rgbTex:
    m_OverrideState: 1
    m_Value: {fileID: 2800000, guid: 181d47e768319424291cbf45dfce2db1, type: 3}
    dimension: 1
  rgbStrength:
    m_OverrideState: 1
    m_Value: 0.15
  scanlineTex:
    m_OverrideState: 0
    m_Value: {fileID: 2800000, guid: 7b5d79ca87223054fa2cb8ee041efb01, type: 3}
    dimension: 1
  scanlineStrength:
    m_OverrideState: 0
    m_Value: 0.2
  scanlineSize:
    m_OverrideState: 1
    m_Value: 6
  scrollSpeed:
    m_OverrideState: 1
    m_Value: 8
  pixelSize:
    m_OverrideState: 1
    m_Value: 1
  randomWear:
    m_OverrideState: 0
    m_Value: 0.27
  aberrationStrength:
    m_OverrideState: 0
    m_Value: 1.95
  trackingTexture:
    m_OverrideState: 0
    m_Value: {fileID: 2800000, guid: 719d505767bc95942a4ff41e91d52eba, type: 3}
    dimension: 1
  trackingSize:
    m_OverrideState: 0
    m_Value: 0.3
  trackingStrength:
    m_OverrideState: 0
    m_Value: 7.8
  trackingSpeed:
    m_OverrideState: 0
    m_Value: 0.1
  trackingJitter:
    m_OverrideState: 0
    m_Value: 0.01
  trackingColorDamage:
    m_OverrideState: 0
    m_Value: 0.1
  trackingLinesThreshold:
    m_OverrideState: 0
    m_Value: 0.9
  trackingLinesColor:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 0.5019608}
  brightness:
    m_OverrideState: 1
    m_Value: 2
  contrast:
    m_OverrideState: 1
    m_Value: 1
  enableInterlacing:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &2182205748910702091
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  skipIterations:
    m_OverrideState: 0
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0.9
  intensity:
    m_OverrideState: 1
    m_Value: 1
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
  clamp:
    m_OverrideState: 0
    m_Value: 65472
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  highQualityFiltering:
    m_OverrideState: 0
    m_Value: 0
  downscale:
    m_OverrideState: 0
    m_Value: 0
  maxIterations:
    m_OverrideState: 0
    m_Value: 6
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &5132782315067736101
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 66f335fb1ffd8684294ad653bf1c7564, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  postExposure:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 0
  colorFilter:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  hueShift:
    m_OverrideState: 0
    m_Value: 0
  saturation:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &8274834959362863516
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3eb4b772797da9440885e8bd939e9560, type: 3}
  m_Name: ColorCurves
  m_EditorClassIdentifier: 
  active: 1
  master:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 3
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 0.4368601
          value: 0.5686273
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  red:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  green:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  blue:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsHue:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsSat:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 1
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  satVsSat:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  lumVsSat:
    m_OverrideState: 0
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0.5
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
