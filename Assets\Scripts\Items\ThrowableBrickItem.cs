using Fusion;
using UnityEngine;
using SimpleFPS;
using DG.Tweening;

public class ThrowableBrickItem : ThrowablePickupItem {
    [Header("Collision")]
    [SerializeField] LayerMask hitMask;
    [SerializeField] private GameObject particleFx;
    [SerializeField] float delaytime = 1f;

    [Header("Player Death Settings")]
    [SerializeField] private bool enablePlayerDeath = false;

    const float G = -9.81f;

    [Networked] Vector3 startPos { get; set; }
    [Networked] Vector3 startVel { get; set; }
    [Networked] float startTime { get; set; }
    [Networked] bool inFlight { get; set; }
    [Networked] bool HasImpacted { get; set; }

    public override NetworkObject ThrowObject(PlayerController player) {
        player.RPC_PlayMeleeAttackAnimation();
        Vector3 pos = player.firstPersonItemHolder.position + player.firstPersonItemHolder.forward;
        Quaternion rot = player.firstPersonItemHolder.rotation;

        NetworkObject obj = Runner.Spawn(sceneObject, pos, rot, player.Object.InputAuthority);

        var proj = obj.GetComponent<ThrowableBrickItem>();
        proj?.InitParabola(pos, player.firstPersonItemHolder.forward * throwForce);

        if (obj.TryGetComponent<Rigidbody>(out var rb)) {
            rb.isKinematic = true;
            rb.useGravity = false;
        }

        return obj;
    }

    private void InitParabola(Vector3 pos, Vector3 vel) {
        startPos = pos;
        startVel = vel;
        startTime = Runner.SimulationTime;
        inFlight = true;
        transform.position = pos;
    }

    public override void FixedUpdateNetwork() {
        if (!inFlight) {
            return;
        }

        float t = Runner.SimulationTime - startTime;
        Vector3 next = startPos + startVel * t + Vector3.up * G * t * t * 0.5f;

        Vector3 prev = transform.position;
        Vector3 dir = next - prev;
        float dist = dir.magnitude;

        if (dist > 0f && Object.HasStateAuthority &&
            Runner.LagCompensation.Raycast(prev,
                                           dir.normalized,
                                           dist,
                                           Object.InputAuthority,
                                           out LagCompensatedHit hit,
                                           hitMask == 0 ? ~0 : hitMask,
                                           HitOptions.IncludePhysX)) {
            HandleCollision(hit);
            return;
        }

        transform.position = next;
    }

    private void HandleCollision(LagCompensatedHit hit) {

        // Despawn the object if it hits a non-physical object.
        if (hit.Collider != null &&
            hit.Collider.gameObject.layer == LayerMask.NameToLayer("Default")) {
            Impact();
            return;
        }

        // If the hit object is a player, show hit markers and despawn the object.
        HitboxRoot root = hit.Hitbox.Root;
        if (root == null) {
            Impact();
            return;
        }

        PlayerController victim = root.GetComponent<PlayerController>();
        PlayerController thrower = Runner.GetPlayerObject(Object.InputAuthority)
                                         ?.GetComponent<PlayerController>();

        if (victim != null && thrower != null) {
            // Only show hitmarker for the thrower, not the victim
            thrower.RPC_ShowHitMarker();

            // Show got hit effect only for the victim
            victim.RPC_ShowGotHit();

            // If player death is enabled, kill the victim
            if (enablePlayerDeath) {
                PlayerDeath playerDeath = victim.GetComponent<PlayerDeath>();
                if (playerDeath != null) {
                    playerDeath.DieFromExplosion(Object.InputAuthority);
                }
            }
        }

        Impact();
    }
    public void Impact() {
        if (!Object.HasStateAuthority || HasImpacted) {
            return;
        }

        HasImpacted = true;
        inFlight = false;

        RPC_Explode(transform.position);

        // Use DOTween instead of coroutine for despawn delay
        DOVirtual.DelayedCall(delaytime, () => {
            if (Object != null && Object.IsValid) {
                Runner.Despawn(Object);
            }
        });
    }

    [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
    void RPC_Explode(Vector3 pos) {
        if (!particleFx) {
            return;
        }

        // Instantiate particle effect at impact position
        GameObject fx = Instantiate(particleFx, pos, particleFx.transform.rotation);

        // Auto-destroy the effect after delay time
        Destroy(fx, delaytime);

        // Disable visual components
        var meshRenderer = GetComponent<MeshRenderer>();
        if (meshRenderer) meshRenderer.enabled = false;

        var collider = GetComponent<Collider>();
        if (collider) collider.enabled = false;
    }

    public override void RequestToUse(PlayerRef playerRef) {
        RPC_RequestPickup(playerRef);
    }

    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    public new void RPC_RequestPickup(PlayerRef playerRef) {
        if (!IsCanBeUse()) {
            return;
        }

        PlayerController player = Runner.GetPlayerObject(playerRef).GetComponent<PlayerController>();

        NetworkObject newItem = null;
        if (inHandTPO.IsValid) {
            Vector3 spawnPos = player.thirdPersonItemHolder.position;
            Quaternion spawnRot = player.thirdPersonItemHolder.rotation;
            newItem = Runner.Spawn(inHandTPO, spawnPos, spawnRot, playerRef);

            player.CurrentItem = newItem;
        }

        Runner.Despawn(Object);
    }
}
